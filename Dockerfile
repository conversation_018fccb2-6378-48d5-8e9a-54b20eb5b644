FROM python:3.10
#-slim

ENV PYTHONUNBUFFERED=1 COLUMNS=200 TZ=Asia/Almaty

COPY ./src/requirements.txt /src/

# hadolint ignore=DL3025,DL3013,DL3008,DL3009,DL3015,DL3042  
RUN apt-get update && apt-get install -y wget ffmpeg libsm6 libxext6 gdal-bin  python3-dev libldap2-dev libsasl2-dev libssl-dev && \
    pip install --upgrade pip && \
    ln -fs /usr/share/zoneinfo/Asia/Almaty /etc/localtime && \
    echo "Asia/Almaty" > /etc/timezone && \
    pip install --no-cache-dir -Ur /src/requirements.txt  && pip install python-ldap


COPY . /src

WORKDIR /src
RUN  python ./manage.py collectstatic --noinput
EXPOSE 8080
CMD ["python", "manage.py", "runserver", "0.0.0.0:8080"]
#CMD ["gunicorn", "--bind", "0.0.0.0:8080", "post_admin.wsgi:application"]
