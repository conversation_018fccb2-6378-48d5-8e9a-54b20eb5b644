from django.db import models
from django.contrib.gis.db.models import GeometryField
from django.contrib.gis.geos import GEOSGeometry

class ChangeloggableMixin(models.Model):
    """Значения полей сразу после инициализации объекта"""
    _original_values = None

    class Meta:
        abstract = True

    def __init__(self, *args, **kwargs):  
        super(ChangeloggableMixin, self).__init__(*args, **kwargs)

        self._original_values = {}
        for field in self._meta.fields:

            if isinstance(field, models.ForeignKey):
                self._original_values[field.name] = (
                    getattr(self, f'{field.name}_id')
                )
            elif isinstance(field, GeometryField):
                geometry = getattr(self, field.name)
                if geometry:
                    self._original_values[field.name]= geometry.transform(4326, clone=True).wkt
                else: 
                    self._original_values[field.name]=getattr(self, field.name)
            else:
                self._original_values[field.name] = getattr(self, field.name)


    def get_changed_fields(self):
        """
        Получаем измененные данные
        """
        result = {}

        for name, value in self._original_values.items():
            
            valued=getattr(self, name)
            if self._meta.get_field(name).get_internal_type()== (
                    'PolygonField'
                ):
                valued=getattr(self, name).transform(4326, clone=True).wkt

            print(value)
            print(valued)
            if value != valued:                   

                temp = {}
                temp[name] = valued

                # Дополнительная проверка для полей Foreign Key
                if self._meta.get_field(name).get_internal_type() == (
                    'ForeignKey'
                ):
                    
                    if value != getattr(self, f'{name}_id'):
                        result.update(temp)
                

                # Для остальных полей просто выдаем результат
                else:
                    result.update(temp)

        return result