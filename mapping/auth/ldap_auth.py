# auth/ldap_auth.py

import ldap
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.contrib.auth.models import Group


def ldap_auth(username=None, password=None):
    ldap_connection = ldap.initialize('ldap://192.168.14.233:3268')
    ldap_connection.set_option(ldap.OPT_PROTOCOL_VERSION, 3)
    ldap_connection.set_option(ldap.OPT_REFERRALS, 0)

    # Bind with the provided username and password
    bind_dn = f'{username}@kazpost.kz'
    ldap_connection.bind_s(bind_dn, password)
    return ldap_connection


def search_ldap_user(ldap_connection, username):
    search_base = 'dc=kazpost,dc=kz'
    search_filter = f'(sAMAccountName={username})'
    attributes = ['displayname', 'mail', 'telephonenumber', 'department', 'company', 'title']
    return ldap_connection.search_s(search_base, ldap.SCOPE_SUBTREE, search_filter, attributes)


def register_user(user, user_info, fio):
    user.first_name = fio[1]
    user.last_name = fio[0]
    user.email = str(user_info.get('mail', [''])[0])
    user.phone_number = user_info.get('telephonenumber', [''])[0]
    user.department = user_info.get('department', [''])[0]
    user.company = user_info.get('company', [''])[0]
    user.title = user_info.get('title', [''])[0]
    user.set_unusable_password()
    user.is_active = True
    user.is_staff = True
    user.save()
    group = Group.objects.get(name='ldap_users')
    user.groups.add(group)
    return user


class LDAPAdminBackend(ModelBackend):

    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # Establish an LDAP connection
            ldap_connection = ldap_auth(username, password)
            # Search for user information
            result = search_ldap_user(ldap_connection, username)
            if result:

                user_info = result[0][1]

                # Create or update a User object in Django's database
                User = get_user_model()
                try:
                    user = User.objects.get(username=username)
                except ObjectDoesNotExist:
                    user = User(username=username)
                    dn = result[0][0]
                    cn = dn.split(',')[0].split('=')[1]
                    fio = cn.split(' ')
                    user = register_user(user, user_info, fio)
                    return user
                return user
        except ldap.LDAPError as e:
            print(f'LDAP Error: {e}')
            return None

    def get_user(self, user_id):
        User = get_user_model()
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
