from django import forms
from django.contrib.auth.forms import AuthenticationForm

class MyAuthenticationForm(AuthenticationForm):
    # Можно переопределить поля формы или добавить дополнительные поля при необходимости
    username = forms.CharField(
        max_length=150,
        label='Логин',
        widget=forms.TextInput(attrs={
        'class': 'form-control login-input',
        'placeholder': 'Введите имя пользователя'
        })
        )
    password = forms.CharField(
        max_length=128,
        label='Пароль',
        widget=forms.PasswordInput(attrs={
        'class': 'form-control login-input',
        'placeholder': 'Введите пароль'
        })
        )