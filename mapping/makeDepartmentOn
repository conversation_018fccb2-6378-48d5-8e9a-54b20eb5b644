from django.contrib.auth.decorators import login_required
from django.contrib.gis.geos import GEOSGeometry
from django.shortcuts import render
from django.http import JsonResponse
import json
from mapping.models import Hubs, Layers, Departments, Region, DepartmentType
import csv 

with open('/home/<USER>/npi/geoservice/mapping/temp/Depatments_tindex.csv', 'r') as file:
    # Use the built-in 'sum' function to count the lines
    line_count = sum(1 for line in file)
    
with open('/home/<USER>/npi/geoservice/mapping/temp/Depatments_tindex.csv') as file_obj: 
    k =0 
    reader_obj = csv.reader(file_obj) 
    for row in reader_obj: 
        csv_id=int(row[0])
        csv_dm_tindex = int(row[1])
        try:
            deps = Departments.objects.filter(id=csv_id)
            #deps = Departments.objects.filter(new_post_code=csv_newpostcode)
            deps[0].dm_tindex=csv_dm_tindex
            #deps[0].save()
            k=k+1
            percent = 100*k/line_count
            progress = f"Progress: {k}/{line_count}, {percent}%"
            print(progress.ljust(len(progress)), end='\r', flush=True)
        except Exception as e:
            print("\n\r")            
            print("  ")
            print("inedx>>"+ str(k))
            print("exception:" + str(e))
            print("csv_id:" + str(csv_id))
            print("csv_type:" + str(csv_dm_tindex))
            print("deps LEN:" + str(len(deps)))
            
            #deps[0].region=reg7[0]
            #print(" Departments.objects.filter(id=csv_id)[0]"+str( Departments.objects.filter(id=csv_id)[0]))
            #print("Region.objects.filter(id=csv_reg)[0]"+str(Region.objects.filter(id=csv_reg)[0]))
            print("\n\r")            
            
        
print("k="+str(k))
        


#deps = Departments.objects.filter(id=csv_id)
#deps.region_id=
#deps.save();

#print("Start count of hubs posylkark:")
#print(len(source_hubs))

