import requests
import json
import time
from django.core.management.base import BaseCommand
from django.contrib.gis.geos import GEOSGeometry
from django.db import transaction
from mapping.models import Region, RegionBoundary


class Command(BaseCommand):
    help = 'Загружает границы регионов Казахстана через Overpass API'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Только показать данные без сохранения в базу',
        )

    def handle(self, *args, **options):
        self.stdout.write('Начинаем загрузку границ через Overpass API...')
        
        if options['dry_run']:
            self.stdout.write('РЕЖИМ ТЕСТИРОВАНИЯ - данные не будут сохранены')

        # Запро<PERSON> к Overpass API для получения административных границ Казахстана
        overpass_query = """
        [out:json][timeout:60];
        (
          relation["ISO3166-1"="KZ"]["admin_level"~"^(2|4)$"]["type"="boundary"];
          relation["place"~"^(city|state)$"]["name:ru"~"Астана|Алматы|Шымкент"];
          relation["admin_level"="4"]["name:ru"~"область$"];
        );
        out geom;
        """

        try:
            self.stdout.write('Отправляем запрос к Overpass API...')
            
            overpass_url = "https://overpass-api.de/api/interpreter"
            response = requests.post(
                overpass_url, 
                data=overpass_query,
                headers={'User-Agent': 'KazPost-GeoService/1.0'},
                timeout=120
            )
            response.raise_for_status()
            
            data = response.json()
            elements = data.get('elements', [])
            
            self.stdout.write(f'Получено {len(elements)} административных единиц')
            
            for element in elements:
                if element.get('type') != 'relation':
                    continue
                    
                tags = element.get('tags', {})
                name_ru = tags.get('name:ru', tags.get('name', ''))
                admin_level = tags.get('admin_level')
                
                if not name_ru:
                    continue
                
                self.stdout.write(f'Обрабатываем: {name_ru} (admin_level: {admin_level})')
                
                # Строим геометрию из координат
                try:
                    geometry = self.build_geometry_from_overpass(element)
                    if not geometry:
                        self.stdout.write(f'  ⚠️ Не удалось построить геометрию для {name_ru}')
                        continue
                        
                    self.stdout.write(f'  ✓ Геометрия: {geometry.geom_type}')
                    
                    if not options['dry_run']:
                        # Ищем соответствующий регион в базе
                        region = self.find_matching_region(name_ru)
                        
                        if region:
                            self.stdout.write(f'  ✓ Найден регион в базе: {region.name} (ID: {region.id})')
                            
                            # Сохраняем границу
                            with transaction.atomic():
                                boundary, created = RegionBoundary.objects.update_or_create(
                                    region=region,
                                    osm_id=element.get('id'),
                                    defaults={
                                        'region_name': name_ru,
                                        'boundary_polygon': geometry,
                                        'osm_type': 'relation',
                                        'admin_level': int(admin_level) if admin_level else None
                                    }
                                )
                                
                                if created:
                                    self.stdout.write(f'  ✓ Граница создана в базе')
                                else:
                                    self.stdout.write(f'  ✓ Граница обновлена в базе')
                        else:
                            self.stdout.write(f'  ⚠️ Регион {name_ru} не найден в базе данных')
                    
                except Exception as e:
                    self.stdout.write(f'  ❌ Ошибка обработки {name_ru}: {e}')
                    
        except requests.RequestException as e:
            self.stdout.write(self.style.ERROR(f'Ошибка запроса к Overpass API: {e}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Общая ошибка: {e}'))
            
        self.stdout.write(self.style.SUCCESS('Загрузка границ завершена!'))

    def build_geometry_from_overpass(self, element):
        """Строит геометрию из данных Overpass API"""
        try:
            # Для relation нужно собрать все way и построить полигон
            members = element.get('members', [])
            if not members:
                return None
                
            # Простая реализация - берем первый outer way
            outer_ways = [m for m in members if m.get('role') == 'outer' and m.get('type') == 'way']
            if not outer_ways:
                return None
                
            # Берем координаты из geometry
            geometry_data = element.get('geometry', [])
            if not geometry_data:
                return None
                
            # Строим координаты полигона
            coordinates = []
            for geom in geometry_data:
                if geom.get('type') == 'way':
                    way_coords = [[node['lon'], node['lat']] for node in geom.get('nodes', [])]
                    coordinates.extend(way_coords)
            
            if len(coordinates) < 3:
                return None
                
            # Замыкаем полигон
            if coordinates[0] != coordinates[-1]:
                coordinates.append(coordinates[0])
                
            # Создаем GeoJSON полигон
            geojson = {
                "type": "Polygon",
                "coordinates": [coordinates]
            }
            
            return GEOSGeometry(json.dumps(geojson))
            
        except Exception as e:
            self.stdout.write(f'Ошибка построения геометрии: {e}')
            return None

    def find_matching_region(self, name_ru):
        """Находит соответствующий регион в базе данных"""
        # Простое сопоставление по ключевым словам
        name_lower = name_ru.lower()
        
        if 'астана' in name_lower or 'нур-султан' in name_lower:
            return Region.objects.filter(name__icontains='астана').first()
        elif 'алматы' in name_lower and 'область' not in name_lower:
            return Region.objects.filter(name__icontains='алматы').exclude(name__icontains='область').first()
        elif 'шымкент' in name_lower:
            return Region.objects.filter(name__icontains='шымкент').first()
        else:
            # Для областей ищем по первому слову
            first_word = name_ru.split()[0]
            return Region.objects.filter(name__icontains=first_word).first()
