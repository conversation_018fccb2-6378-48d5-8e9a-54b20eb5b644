import requests
import json
import time
from django.core.management.base import BaseCommand
from django.contrib.gis.geos import GEOSGeometry
from django.db import transaction
from mapping.models import Region, RegionBoundary


class Command(BaseCommand):
    help = 'Загружает границы регионов Казахстана из OpenStreetMap'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Только показать данные без сохранения в базу',
        )

    def handle(self, *args, **options):
        self.stdout.write('Начинаем загрузку границ регионов Казахстана...')
        
        # Список регионов Казахстана для поиска
        kazakhstan_regions = [
            'Астана', 'Алматы', 'Шымкент',  # Города республиканского значения
            'Акмолинская область', 'Актюбинская область', 'Алматинская область',
            'Атырауская область', 'Восточно-Казахстанская область', 'Жамбылская область',
            'Западно-Казахстанская область', 'Карагандинская область', 'Костанайская область',
            'Кызылординская область', 'Мангистауская область', 'Павлодарская область',
            'Северо-Казахстанская область', 'Туркестанская область'
        ]

        if options['dry_run']:
            self.stdout.write('РЕЖИМ ТЕСТИРОВАНИЯ - данные не будут сохранены')

        for region_name in kazakhstan_regions:
            self.stdout.write(f'Загружаем границы для: {region_name}')
            
            try:
                # Запрос к Nominatim API для получения границ
                url = 'https://nominatim.openstreetmap.org/search'
                params = {
                    'q': f'{region_name}, Казахстан',
                    'format': 'json',
                    'polygon_geojson': 1,
                    'addressdetails': 1,
                    'limit': 1
                }

                # Добавляем правильные заголовки для Nominatim
                headers = {
                    'User-Agent': 'KazPost-GeoService/1.0 (<EMAIL>)',
                    'Accept': 'application/json',
                    'Accept-Language': 'ru,en'
                }

                response = requests.get(url, params=params, headers=headers, timeout=30)
                response.raise_for_status()
                data = response.json()

                # Пауза между запросами (требование Nominatim)
                time.sleep(1)
                
                if not data:
                    self.stdout.write(
                        self.style.WARNING(f'Границы для {region_name} не найдены')
                    )
                    continue
                
                result = data[0]
                
                if 'geojson' not in result:
                    self.stdout.write(
                        self.style.WARNING(f'Геометрия для {region_name} отсутствует')
                    )
                    continue
                
                # Создаем геометрию из GeoJSON
                geojson = result['geojson']
                geometry = GEOSGeometry(json.dumps(geojson))
                
                # Информация о найденном объекте
                display_name = result.get('display_name', '')
                osm_type = result.get('osm_type', '')
                osm_id = result.get('osm_id', '')
                
                self.stdout.write(f'  Найдено: {display_name}')
                self.stdout.write(f'  OSM: {osm_type}/{osm_id}')
                self.stdout.write(f'  Тип геометрии: {geometry.geom_type}')
                
                if not options['dry_run']:
                    # Ищем соответствующий регион в базе
                    try:
                        region = Region.objects.filter(
                            name__icontains=region_name.split()[0]  # Первое слово
                        ).first()

                        if region:
                            self.stdout.write(f'  Найден регион в базе: {region.name} (ID: {region.id})')

                            # Сохраняем границу региона
                            with transaction.atomic():
                                boundary, created = RegionBoundary.objects.update_or_create(
                                    region=region,
                                    osm_id=osm_id,
                                    defaults={
                                        'region_name': region_name,
                                        'boundary_polygon': geometry,
                                        'osm_type': osm_type,
                                        'admin_level': result.get('address', {}).get('admin_level')
                                    }
                                )

                                if created:
                                    self.stdout.write(f'  ✓ Граница создана в базе')
                                else:
                                    self.stdout.write(f'  ✓ Граница обновлена в базе')
                        else:
                            self.stdout.write(
                                self.style.WARNING(f'  Регион {region_name} не найден в базе данных')
                            )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'  Ошибка сохранения границы: {e}')
                        )
                
                self.stdout.write(self.style.SUCCESS(f'✓ {region_name} обработан'))
                
            except requests.RequestException as e:
                self.stdout.write(
                    self.style.ERROR(f'Ошибка запроса для {region_name}: {e}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Ошибка обработки {region_name}: {e}')
                )
        
        self.stdout.write(self.style.SUCCESS('Загрузка границ завершена!'))
