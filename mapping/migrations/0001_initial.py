# Generated by Django 4.2.4 on 2023-10-18 11:49

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Departments',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('code', models.CharField(blank=True, max_length=50, null=True)),
                ('post_code', models.CharField(blank=True, max_length=15, null=True)),
                ('new_post_code', models.CharField(blank=True, max_length=50, null=True)),
                ('lat', models.FloatField(blank=True, null=True)),
                ('lon', models.FloatField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Отделение',
                'verbose_name_plural': 'Отделения',
                'db_table': 'department',
            },
        ),
        migrations.CreateModel(
            name='Layers',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(blank=True, max_length=255)),
                ('name', models.CharField(blank=True, max_length=255)),
            ],
            options={
                'verbose_name': 'Слой',
                'verbose_name_plural': 'Слои',
                'db_table': 'layers',
            },
        ),
        migrations.CreateModel(
            name='Hubs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True, help_text='created', verbose_name='created')),
                ('modified', models.DateTimeField(auto_now=True, help_text='modified', verbose_name='modified')),
                ('polygon', django.contrib.gis.db.models.fields.PolygonField(null=True, srid=4326, verbose_name='polygon')),
                ('is_active', models.BooleanField(default=True)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='mapping.departments')),
                ('layers', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='mapping.layers')),
            ],
            options={
                'verbose_name': 'Полигон',
                'verbose_name_plural': 'Полигоны',
                'db_table': 'hubs',
            },
        ),
    ]
