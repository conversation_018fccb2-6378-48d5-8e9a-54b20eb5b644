# Generated by Django 3.2 on 2023-11-11 09:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mapping', '0002_departments_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Регион',
                'verbose_name_plural': 'Регионы',
                'db_table': 'region',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Auto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('equipment_num', models.Char<PERSON>ield(blank=True, max_length=18, null=True)),
                ('name_tech_object', models.Char<PERSON><PERSON>(blank=True, max_length=40, null=True)),
                ('sing_of_ownership', models.CharField(blank=True, max_length=1, null=True)),
                ('date_change', models.CharField(blank=True, max_length=8, null=True)),
                ('manufacture_type', models.CharField(blank=True, max_length=20, null=True)),
                ('manufacture_date', models.CharField(blank=True, max_length=4, null=True)),
                ('inventory_number', models.CharField(blank=True, max_length=25, null=True)),
                ('branch', models.CharField(blank=True, max_length=4, null=True)),
                ('tech_place', models.CharField(blank=True, max_length=25, null=True)),
                ('parent_tech_place', models.CharField(blank=True, max_length=25, null=True)),
                ('cost_center', models.CharField(blank=True, max_length=10, null=True)),
                ('license_num', models.CharField(blank=True, max_length=15, null=True)),
                ('vin', models.CharField(blank=True, max_length=30, null=True)),
                ('imei', models.CharField(blank=True, max_length=18, null=True)),
                ('kind', models.CharField(blank=True, max_length=10, null=True)),
                ('load_weight', models.CharField(blank=True, max_length=13, null=True)),
                ('weight_unit', models.CharField(blank=True, max_length=3, null=True)),
                ('load_volume', models.CharField(blank=True, max_length=13, null=True)),
                ('volume_unit', models.CharField(blank=True, max_length=3, null=True)),
                ('engine_type', models.CharField(blank=True, max_length=10, null=True)),
                ('engine_capacity', models.CharField(blank=True, max_length=13, null=True)),
                ('engice_capacity_unit', models.CharField(blank=True, max_length=3, null=True)),
                ('category', models.CharField(blank=True, max_length=20, null=True)),
                ('fuel_consumption_rates', models.CharField(blank=True, max_length=30, null=True)),
                ('status', models.CharField(blank=True, max_length=30, null=True)),
                ('manufacturer', models.CharField(blank=True, max_length=30, null=True)),
                ('stats', models.CharField(blank=True, max_length=20, null=True)),
                ('skif_id', models.UUIDField(blank=True, null=True)),
                ('last_lon', models.DecimalField(blank=True, decimal_places=1000, max_digits=1000, null=True)),
                ('last_lat', models.DecimalField(blank=True, decimal_places=1000, max_digits=1000, null=True)),
                ('date_point', models.DateTimeField(blank=True, null=True)),
                ('date_update_coodr', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Транспорт',
                'verbose_name_plural': 'Транспорт',
                'db_table': 'auto',
            },
        ),
    ]
