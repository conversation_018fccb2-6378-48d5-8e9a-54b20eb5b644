# Generated by Django 3.2 on 2023-11-11 09:25

from django.db import migrations

def add_comments(apps, schema_editor):
    Auto = apps.get_model('mapping', 'Auto')
    db_alias = schema_editor.connection.alias

    # Добавляем комментарии к полям модели Auto
    schema_editor.execute("COMMENT ON COLUMN auto.equipment_num IS 'Номер единицы оборудования (EQUNR)';")
    schema_editor.execute("COMMENT ON COLUMN auto.name_tech_object IS 'Название технического объекта (EQKTX)';")
    schema_editor.execute("COMMENT ON COLUMN auto.sing_of_ownership IS 'Признак собственного, либо арендованного ТС (EQTYP)';")
    schema_editor.execute("COMMENT ON COLUMN auto.date_change IS 'Дата последнего изменения (AEDAT)';")
    schema_editor.execute("COMMENT ON COLUMN auto.manufacture_type IS 'Название типа изготовителя (TYPBZ)';")
    schema_editor.execute("COMMENT ON COLUMN auto.manufacture_date IS 'Год выпуска (BAUJJ)';")
    schema_editor.execute("COMMENT ON COLUMN auto.inventory_number IS 'Инвентарный номер/Паспорт (INVNR)';")
    schema_editor.execute("COMMENT ON COLUMN auto.branch IS 'Филиал (IWERK)';")
    schema_editor.execute("COMMENT ON COLUMN auto.tech_place IS 'Код технического места (TPLNR)';")
    schema_editor.execute("COMMENT ON COLUMN auto.parent_tech_place IS 'Вышестоящее техническое место (TPLMA)';")
    schema_editor.execute("COMMENT ON COLUMN auto.cost_center IS 'Место возникновения затрат (KOSTL)';")
    schema_editor.execute("COMMENT ON COLUMN auto.license_num IS 'Номерной знак транспортного средства (LICENSE_NUM)';")
    schema_editor.execute("COMMENT ON COLUMN auto.vin IS 'Ид. номер изготовителя для транспортного средства (VIN) (FLEET_VIN)';")
    schema_editor.execute("COMMENT ON COLUMN auto.imei IS 'IMEI код GPS треккера (FLEET_NUM)';")
    schema_editor.execute("COMMENT ON COLUMN auto.kind IS 'Вид транспортного средства (FLEET_CAT)';")
    schema_editor.execute("COMMENT ON COLUMN auto.load_weight IS 'Вес объекта, тн (грузоподъемность ТС) (LOAD_WGT)';")
    schema_editor.execute("COMMENT ON COLUMN auto.weight_unit IS 'Единица веса (WGT_UNIT)';")
    schema_editor.execute("COMMENT ON COLUMN auto.load_volume IS 'Объем полезного объема, м3 (вручную) (LOAD_VOL)';")
    schema_editor.execute("COMMENT ON COLUMN auto.volume_unit IS 'Единица объема (VOL_UNIT)';")
    schema_editor.execute("COMMENT ON COLUMN auto.engine_type IS 'Вид двигателя (ENGINE_TYPE)';")
    schema_editor.execute("COMMENT ON COLUMN auto.engine_capacity IS 'Рабочий объем, м3 (ENGINE_CAP)';")
    schema_editor.execute("COMMENT ON COLUMN auto.engice_capacity_unit IS 'Единица измерения для литража (UNIT_CAP)';")
    schema_editor.execute("COMMENT ON COLUMN auto.category IS 'Категория ТС (ZS_PM_CATTS)';")
    schema_editor.execute("COMMENT ON COLUMN auto.fuel_consumption_rates IS 'Нормы расхода топлива л/100км (ZS_PM_CNVL)';")
    schema_editor.execute("COMMENT ON COLUMN auto.status IS 'Статус автотранспорта (USER_LINE)';")
    schema_editor.execute("COMMENT ON COLUMN auto.manufacturer IS 'Изготовитель (HERST)';")
    schema_editor.execute("COMMENT ON COLUMN auto.stats IS ' (STATS)';")
    schema_editor.execute("COMMENT ON COLUMN auto.skif_id IS 'id транспортного средства в skif.pro';")
    schema_editor.execute("COMMENT ON COLUMN auto.last_lon IS NULL;")
    schema_editor.execute("COMMENT ON COLUMN auto.last_lat IS NULL;")
    schema_editor.execute("COMMENT ON COLUMN auto.date_point IS 'Дата создания/обновления координат';")
    schema_editor.execute("COMMENT ON COLUMN auto.date_update_coodr IS 'Дата обновления координат';")
    # ... добавьте комментарии к остальным полям аналогичным образом


class Migration(migrations.Migration):

    dependencies = [
        ('mapping', '0003_auto_region'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='region',
            options={'verbose_name': 'Регион', 'verbose_name_plural': 'Регионы'},
        ),
        migrations.RunPython(add_comments),
    ]
