# Generated by Django 3.2 on 2023-11-14 19:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mapping', '0005_departments_region'),
    ]

    operations = [
        migrations.CreateModel(
            name='DepartmentType',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('code', models.Char<PERSON>ield(max_length=50)),
            ],
            options={
                'db_table': 'department_type',
                'managed': False,
            },
        ),
        migrations.AddField(
            model_name='departments',
            name='class_field',
            field=models.CharField(blank=True, db_column='class', max_length=5, null=True),
        ),
        migrations.AddField(
            model_name='departments',
            name='location_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='departments',
            name='type',
            field=models.ForeignKey(blank=True, db_column='type', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='mapping.departmenttype'),
        ),
    ]
