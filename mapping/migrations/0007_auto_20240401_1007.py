# Generated by Django 3.2 on 2024-04-01 10:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('mapping', '0006_auto_20231114_1926'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='departmenttype',
            options={'managed': False, 'verbose_name': 'Тип отделения', 'verbose_name_plural': 'Типы отделений'},
        ),
        migrations.AddField(
            model_name='departments',
            name='dm_tindex',
            field=models.CharField(blank=True, default='------', max_length=7, null=True, verbose_name='Технологический index'),
        ),
        migrations.CreateModel(
            name='UserRegionAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('has_full_access', models.BooleanField(default=False, verbose_name='Полный доступ')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mapping.region', verbose_name='Регион')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Доступы по регионам',
                'verbose_name_plural': 'Доступы по регионам',
                'db_table': 'user_region_access',
            },
        ),
        migrations.CreateModel(
            name='ProductCategoryLayers',
            fields=[
                ('product_code', models.CharField(max_length=20, primary_key=True, serialize=False, verbose_name='Код продукта')),
                ('product_name', models.CharField(max_length=20, verbose_name='Наименование продукта')),
                ('layer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='mapping.layers', verbose_name='Слои')),
            ],
            options={
                'verbose_name': 'Продукты по слоям',
                'verbose_name_plural': 'Продукты по слоям',
                'db_table': 'adr"."product_category_layer',
            },
        ),
    ]
