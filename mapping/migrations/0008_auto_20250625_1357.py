# Generated by Django 3.2 on 2025-06-25 08:57

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mapping', '0007_auto_20240401_1007'),
    ]

    operations = [
        migrations.CreateModel(
            name='HubsMapEditor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'Редактор карты хабов',
                'verbose_name_plural': '🗺️ Групповое редактирование полигонов',
                'managed': False,
            },
        ),
        migrations.AddField(
            model_name='departments',
            name='sub_department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='mapping.departments', verbose_name='замещающее отделение'),
        ),
        migrations.AddField(
            model_name='region',
            name='lat',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='region',
            name='lon',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='productcategorylayers',
            name='product_name',
            field=models.CharField(max_length=100, verbose_name='Наименование продукта'),
        ),
        migrations.AlterField(
            model_name='userregionaccess',
            name='region',
            field=models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='mapping.region', verbose_name='Регион'),
        ),
        migrations.CreateModel(
            name='RegionBoundary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('region_name', models.CharField(max_length=100, verbose_name='Название региона')),
                ('boundary_polygon', django.contrib.gis.db.models.fields.PolygonField(srid=4326, verbose_name='Полигон границы')),
                ('osm_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='Тип OSM')),
                ('osm_id', models.BigIntegerField(blank=True, null=True, verbose_name='ID OSM')),
                ('admin_level', models.IntegerField(blank=True, null=True, verbose_name='Административный уровень')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата создания')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата обновления')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mapping.region', verbose_name='Регион')),
            ],
            options={
                'verbose_name': 'Граница региона',
                'verbose_name_plural': 'Границы регионов',
                'db_table': 'region_boundaries',
                'unique_together': {('region', 'osm_id')},
            },
        ),
    ]
