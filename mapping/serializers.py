from typing import Tuple

from rest_framework import serializers

from .models import Hubs



class HubSerializer(serializers.ModelSerializer):
    center = serializers.CharField()
    geo_polygon = serializers.CharField()
    available = serializers.BooleanField(default=False)
    count = serializers.IntegerField(required=False)

    class Meta:
        model = Hubs
        fields = (
            'id', 'created', 'modified', 'title', 'center', 'slug',
            'hub_rating', 'geo_polygon', 'available', 'count',
        )


class SimpleHubSerializer(serializers.ModelSerializer):
    class Meta:
        model = Hubs
        fields = ('id', 'title')
