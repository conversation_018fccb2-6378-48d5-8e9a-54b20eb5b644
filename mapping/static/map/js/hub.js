django.jQuery(document).ready(function() {
    var $ = django.jQuery;
    var area_selector = $('#id_area');
    var banned_area_selector = $('#id_banned_area');
    area_selector.closest('.form-row').hide();
    banned_area_selector.closest('.form-row').hide();
    $('#id_polygon_div_map').find('.clear_features').hide();
    $('#id_layers').on('change', function() {
        var selectedLayer = $(this).val();
        if (selectedLayer) {
            // Очистка всех полигонов
            select.getFeatures().clear();

            // Загрузка и добавление полигонов, связанных с выбранным слоем
            var url = '/url/to/get/layer/polygons/?layer_id=' + selectedLayer;  // Замените на ваш реальный URL
            $.getJSON(url, function(data) {
                data.forEach(function(polygon) {
                    var feature = new ol.Feature({
                        geometry: new ol.geom.Polygon(polygon.geometry.coordinates)
                    });
                    feature.set('pk', polygon.id);
                    select.getFeatures().push(feature);
                });
            });
        }
    });

    if (ol) {
        var area_selected = function() {
            var selected = [];

            var $selected = area_selector.find('option:selected');
            for(var i = 0; i < $selected.length; i++){
                selected.push($selected[i].value);
            }
            return selected
        }

        var area_select = function() {
            var $selected = area_selector.find('option').removeAttr('selected');
            var selected = [];

            select.getFeatures().forEach(function(feature) {
                selected.push(area_selector.find('option[value=' + feature.get('pk') + ']').val())
            });
            area_selector.val(selected);
        };

	    var banned_area_selected = function() {
	    	var selected = [];

	    	var $selected = banned_area_selector.find('option:selected');
			for(var i = 0; i < $selected.length; i++){
				selected.push($selected[i].value);
			}
	    	return selected
	    }

	    var banned_area_select = function() {
	    	var $selected = banned_area_selector.find('option').removeAttr('selected');
	    	var selected = [];

	    	select2.getFeatures().forEach(function(feature) {
	    		selected.push(banned_area_selector.find('option[value=' + feature.get('pk') + ']').val())
	    	});
	    	banned_area_selector.val(selected);
	    };

	    (function() {
	    	var slug = $("#id_slug");
	    	$('#id_title').on('input', function() {
	    		slug.val(URLify(this.value, 50));
	    	});
	    }());

    	var url = $('#id_features_url').val();
        featureOverlay = new ol.layer.Vector({
            map: geodjango_polygon.map,
            source: new ol.source.Vector({
              url: url,
              format: new ol.format.GeoJSON(),
              wrapX: false
            }),
            updateWhileAnimating: true,
            updateWhileInteracting: true
        });

        featureOverlay.once('change', function(obj) {
            features =obj.target.getSource().getFeatures();
            features.forEach(function(feature) {
                if (area_selected().indexOf(feature.get('pk')) + 1)
                    select.getFeatures().push(feature);
            })
        })

        featureOverlay.once('change', function(obj) {
            features = obj.target.getSource().getFeatures();
            features.forEach(function(feature) {
                if (banned_area_selected().indexOf(feature.get('pk')) + 1)
                	select2.getFeatures().push(feature);
            })
        })

        interactions = geodjango_polygon.map.getInteractions();
        interactions.forEach(function(interaction){
        	if (interaction instanceof ol.interaction.Draw) {
        		geodjango_polygon.map.removeInteraction(interaction);
        	}
        })

        var select = new ol.interaction.Select({
            style: new ol.style.Style({fill: new ol.style.Fill({color: [0,255,0,0.5]})}),
            condition: ol.events.condition.click,
            filter: function (feat, layer) {
                is_feature = false
                featureOverlay.getSource().getFeatures().forEach(function(feature){
                    if(feat == feature)
                        is_feature = true
                });
                return is_feature
            },
            addCondition: function(event) {
                var to_feature = [], is_selected;
                featureOverlay.getSource().getFeatures().forEach(function(feature) {
                    if(feature.getGeometry().intersectsCoordinate(event.coordinate)){
                        to_feature.push(feature);
                    }
                })
                select.getFeatures().forEach(function(feature) {
                    for (i in to_feature)
                        if (feature == to_feature[i]) {
                            is_selected = true;
                        }
                })
                if (is_selected) {
                    return false
                }
                return true
            },
            removeCondition: function(event) {
                var to_pop;
                select.getFeatures().forEach(function(feature) {
                    if(feature.getGeometry().intersectsCoordinate(event.coordinate)){
                        to_pop = feature;
                    }
                })
                if (to_pop) {
                    select.getFeatures().remove(to_pop);
                    area_select();
                    return true
                }
                return false
            },
            toggleCondition: ol.events.condition.never,
            multi: true
        });

        var select2 = new ol.interaction.Select({
	        style: new ol.style.Style({fill: new ol.style.Fill({color: [255,0,0,0.5]})}),
    		condition: function(event) {
                return ol.events.condition.click(event) && ol.events.condition.platformModifierKeyOnly(event);
            },
	        filter: function (feat, layer) {
	        	is_feature = false
	        	featureOverlay.getSource().getFeatures().forEach(function(feature){
	        		if(feat == feature)
	        			is_feature = true
	        	});
                return is_feature
	        },
	        addCondition: function(event) {
        		var to_feature = [], is_selected;
	        	featureOverlay.getSource().getFeatures().forEach(function(feature) {
	        		if(feature.getGeometry().intersectsCoordinate(event.coordinate)){
	        			to_feature.push(feature);
	        		}
	        	})
	        	select2.getFeatures().forEach(function(feature) {
	        		for (i in to_feature)
		        		if (feature == to_feature[i]) {
		        			is_selected = true;
		        		}
	        	})
	        	if (is_selected) {
	        		return false
	        	}
	        	return true
	        },
	        removeCondition: function(event) {
        		var to_pop;
	        	select2.getFeatures().forEach(function(feature) {
	        		if(feature.getGeometry().intersectsCoordinate(event.coordinate)){
	        			to_pop = feature;
	        		}
	        	})
	        	if (to_pop) {
	        		select2.getFeatures().remove(to_pop);
	        		banned_area_select();
	        		return true
	        	}
	        	return false
	        },
	        toggleCondition: ol.events.condition.never,
	        multi: true
	    });

        select.on('select', function(data){
            area_select();
        })

	    select2.on('select', function(data){
	    	banned_area_select();
	    })

        geodjango_polygon.map.addInteraction(select);
        geodjango_polygon.map.addInteraction(select2);
    }
});