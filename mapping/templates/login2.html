<!-- login.html -->
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login</title>
    <link rel="stylesheet" href="{% static 'map/css/bootstrap.min.css' %}">
    <script src="{% static 'map/js/jquery-3.7.0.min.js' %}"></script>
    <script src="{% static 'map/js/bootstrap.min.js' %}"></script>
    <style>
        body {
            overflow-x: hidden; /* Отключение горизонтального скроллинга */
        }
        .login-input { 
            border-radius: 15px;
            margin-top: 1px;
            height: 40px;
            background: rgba(243, 243, 244, 1);
        }
        .btn-login {
            background: rgba(30, 73, 196, 1);
            color: white;
            border-radius: 15px;
        }
        .center-flexbox {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        @media (max-width: 600px) {
            .mixplat__operators {
                display: none;
            }
        }
        .error_login { 
            border-radius: 15px;
            margin: 5px;
            background: rgba(243, 243, 244, 1);
            padding: 10px;
        }
        .error_mess {
            padding-left: 5px;
        }
        .responsive-img {
            width: 100%;
            height: auto;
            object-fit: contain; /* Сохраняет соотношение сторон и масштабирует изображение */
            max-height: 100%; /* Устанавливает максимальную высоту в 100% */
        }
        .col-md-4-custom {
            max-width: 100%; /* Убирает возможное переполнение */
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="row g-0">
            <div class="col-md-8 mixplat__operators" style="background-color: #1e4ac4; height: 100vh;">
                <img src="{% static 'map/img/newgeo.svg' %}" alt="Responsive image" class="responsive-img"> 
            </div>
            <div class="col-md-4 col-md-4-custom d-flex justify-content-center align-items-center" style="height: 98vh;">
                <div style="max-width: 400px; min-width: 399px; font-size: 14px;">
                    <div class="text-center mb-4">
                        <img src="{% static 'map/img/logo.png' %}" class="" >
                    </div>
                    <div>
                        <form method="post">        
                                {% csrf_token %}
                                <!-- Применение класса Bootstrap к форме -->
                                <div class="form-group mb-3" >
                                    <label for="id_username" class="fw-bold">Логин</label>
                                    {{ form.username }}
                                </div>
                                <!-- Применение класса Bootstrap к форме -->
                                <div class="form-group mb-3" >
                                    <label for="id_password" class="fw-bold">Пароль</label>
                                    {{ form.password }}
                                </div>
                                
                                <!-- Применение класса Bootstrap к кнопке -->
                                <button type="submit" class="btn btn-login mt-3 btn-block w-100">Войти</button>

                                {% if form.non_field_errors %}
                                    <div class="row error_login mt-3">
                                        <div class="col-auto">
                                            <img src="{% static 'map/img/error_outline.png' %}" class="">
                                            
                                        </div>
                                        <div class="col error_mess" style="font-size: 14px;">
                                            {% for error in form.non_field_errors %}
                                                {{ error }}
                                            {% endfor %}
                                            
                                        </div>
                                    </div>
                                    
                                {% endif %}

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    
</body>
</html>
