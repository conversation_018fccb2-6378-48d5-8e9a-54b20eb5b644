"""
Django settings for post_admin project.

Generated by 'django-admin startproject' using Django 4.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
from pathlib import Path
import ldap
# from django_auth_ldap.config import LDAPSearch, GroupOfNamesType

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

GDAL_LIBRARY_PATH = os.path.join(BASE_DIR, '.venv', 'Lib', 'site-packages', 'osgeo', 'gdal.dll')
GEOS_LIBRARY_PATH = os.path.join(BASE_DIR, '.venv', 'Lib', 'site-packages', 'osgeo', 'geos_c.dll')
os.environ['PROJ_LIB'] = os.path.join(BASE_DIR, '.venv', 'Lib', 'site-packages', 'osgeo', 'data', 'proj')
if os.getenv('NODE_ENV', 'local')=='local':
    from dotenv import load_dotenv
    load_dotenv()

KAZPOSTGEO_PREFIX=os.getenv('KAZPOSTGEO_PREFIX', '')
ENV_PREFIX=os.getenv('ENV_PREFIX', '')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insgfre-!%vae(s#hoer--r%b&zcaq@4n6m*m$4i=j4$6bk4#dlff7q*(%c'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True


AUTHENTICATION_BACKENDS = [
    # 'mapping.auth.ldap_auth.LDAPAdminBackend',
    'django.contrib.auth.backends.ModelBackend',  # Стандартный бэкенд Django
    # 'mapping.views.CustomAuthBackend',  # Путь к вашему кастомному бэкенду
]
LOGIN_URL = '/'+KAZPOSTGEO_PREFIX+'/login/'

LOGIN_REDIRECT_URL = '/'+KAZPOSTGEO_PREFIX+'/layer_map/'


 

ALLOWED_HOSTS = ['services.post.kz', '127.0.0.1','open.post.kz']
CSRF_TRUSTED_ORIGINS = ['https://services.post.kz','https://open.post.kz']



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'mapping',
    'leaflet',
    'django.contrib.gis',
    'dal',
    'dal_select2',
    'changelog',

]

REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly'
    ]
}

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'changelog.middleware.LoggedInUserMiddleware'
]

ROOT_URLCONF = 'post_admin.urls'



TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'mapping.context_processors.custom_context_settings',
            ],
        },
    },
]

WSGI_APPLICATION = 'post_admin.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases


DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.getenv('DB_NAME', ''),
        'USER': os.getenv('DB_USER', ''),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),
        'HOST': os.getenv('DB_HOST', ''),
        'PORT': os.getenv('DB_PORT', ''),
    }
}





# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'ru-RU'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

# STATIC_URL = '/'+KAZPOSTGEO_PREFIX+'/static/'
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
